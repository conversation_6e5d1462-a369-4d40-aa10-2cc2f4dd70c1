#!/bin/bash

# Script untuk menjalankan semua services dengan Docker Compose

echo "🚀 Starting ATMA Backend Services..."

# Check if .env file exists
if [ ! -f .env ]; then
    echo "❌ .env file not found! Please create .env file first."
    echo "📝 Copy from .env.example if available"
    exit 1
fi

# Stop any existing containers
echo "⏹️  Stopping existing containers..."
docker-compose down

# Pull latest images
echo "📥 Pulling latest images..."
docker-compose pull

# Build services
echo "🔨 Building services..."
docker-compose build

# Start services
echo "▶️  Starting services..."
docker-compose up -d

# Wait for services to be ready
echo "⏳ Waiting for services to initialize..."
sleep 30

# Check status
echo "📊 Service Status:"
docker-compose ps

echo ""
echo "✅ Services started!"
echo ""
echo "🔗 Service URLs:"
echo "   API Gateway: http://localhost:3000"
echo "   Auth Service: http://localhost:3001"
echo "   Archive Service: http://localhost:3002"
echo "   Assessment Service: http://localhost:3003"
echo "   Notification Service: http://localhost:3005"
echo "   Chatbot Service: http://localhost:3006"
echo "   Documentation: http://localhost:8080"
echo ""
echo "🗄️  Database & Infrastructure:"
echo "   PostgreSQL: localhost:5432"
echo "   RabbitMQ Management: http://localhost:15672"
echo "   Redis: localhost:6379"
echo ""
echo "📋 Useful commands:"
echo "   View logs: docker-compose logs -f [service-name]"
echo "   Stop services: docker-compose down"
echo "   Restart service: docker-compose restart [service-name]"
