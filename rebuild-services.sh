#!/bin/bash

# Script untuk rebuild services yang diperlukan setelah perbaikan endpoint

echo "🔧 Rebuilding services after fixing endpoint issues..."

# Stop services yang perlu di-rebuild
echo "⏹️  Stopping services..."
docker-compose stop chatbot-service archive-service

# Rebuild services
echo "🔨 Rebuilding chatbot-service..."
docker-compose build --no-cache chatbot-service

echo "🔨 Rebuilding archive-service..."
docker-compose build --no-cache archive-service

# Start services kembali
echo "▶️  Starting services..."
docker-compose up -d chatbot-service archive-service

# Tunggu services siap
echo "⏳ Waiting for services to be ready..."
sleep 10

# Check status
echo "📊 Checking service status..."
docker-compose ps chatbot-service archive-service

echo "✅ Services rebuilt and started!"
echo ""
echo "🔍 You can check logs with:"
echo "   docker-compose logs -f chatbot-service"
echo "   docker-compose logs -f archive-service"
echo ""
echo "🧪 Test the endpoint with:"
echo "   curl -X GET http://localhost:3000/archive/results/YOUR_RESULT_ID \\"
echo "        -H \"Authorization: Bearer YOUR_JWT_TOKEN\""
