#!/bin/bash

# Script untuk testing endpoint archive/results/:id

echo "🧪 Testing archive/results/:id endpoint..."

# Check if services are running
echo "📊 Checking service status..."
docker-compose ps | grep -E "(archive-service|chatbot-service|api-gateway)"

echo ""
echo "🔍 Testing endpoint accessibility..."

# Test health endpoint first
echo "1. Testing archive service health..."
curl -s http://localhost:3002/archive/health | jq '.' || echo "❌ Archive service not responding"

echo ""
echo "2. Testing API Gateway health..."
curl -s http://localhost:3000/health | jq '.' || echo "❌ API Gateway not responding"

echo ""
echo "📝 To test the actual endpoint, you need:"
echo "   1. A valid JWT token"
echo "   2. A valid result ID from notification"
echo ""
echo "Example command:"
echo "curl -X GET http://localhost:3000/archive/results/YOUR_RESULT_ID \\"
echo "     -H \"Authorization: Bearer YOUR_JWT_TOKEN\" \\"
echo "     -H \"Content-Type: application/json\""
echo ""
echo "Or for internal service call:"
echo "curl -X GET http://localhost:3002/archive/results/YOUR_RESULT_ID \\"
echo "     -H \"X-Internal-Service: true\" \\"
echo "     -H \"X-Service-Key: YOUR_INTERNAL_SERVICE_KEY\" \\"
echo "     -H \"Content-Type: application/json\""
